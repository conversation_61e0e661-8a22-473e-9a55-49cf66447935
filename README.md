<div align="center">

  <h1>🪟 Windows-MCP</h1>

  <a href="https://github.com/CursorTouch/Windows-MCP/blob/main/LICENSE">
    <img src="https://img.shields.io/badge/license-MIT-green" alt="许可证">
  </a>
  <img src="https://img.shields.io/badge/python-3.13%2B-blue" alt="Python">
  <img src="https://img.shields.io/badge/platform-Windows%207–11-blue" alt="平台: Windows 7 到 11">
  <img src="https://img.shields.io/github/last-commit/CursorTouch/Windows-MCP" alt="最后提交">
  <br>
  <a href="https://x.com/CursorTouch">
    <img src="https://img.shields.io/badge/follow-%40CursorTouch-1DA1F2?logo=twitter&style=flat" alt="在 Twitter 上关注">
  </a>
  <a href="https://discord.com/invite/Aue9Yj2VzS">
    <img src="https://img.shields.io/badge/Join%20on-Discord-5865F2?logo=discord&logoColor=white&style=flat" alt="加入我们的 Discord">
  </a>

</div>

<br>

**Windows MCP** 是一个轻量级的开源项目，能够实现 AI 代理与 Windows 操作系统之间的无缝集成。作为 MCP 服务器，它在大语言模型（LLM）和 Windows 操作系统之间架起了桥梁，允许代理执行诸如**文件导航、应用程序控制、UI 交互、QA 测试**等任务。

## 更新

- 试试 🪟[Windows-Use](https://github.com/CursorTouch/Windows-Use)！！这是一个使用 Windows-MCP 构建的代理。
- Windows-MCP 现在作为桌面扩展在 `Claude Desktop` 中提供。

### 支持的操作系统

- Windows 7
- Windows 8, 8.1
- Windows 10
- Windows 11

## 🎥 演示

<https://github.com/user-attachments/assets/d0e7ed1d-6189-4de6-838a-5ef8e1cad54e>

<https://github.com/user-attachments/assets/d2b372dc-8d00-4d71-9677-4c64f5987485>

## ✨ 主要特性

- **无缝 Windows 集成**
  原生与 Windows UI 元素交互，打开应用程序，控制窗口，模拟用户输入等。

- **使用任何 LLM（视觉可选）**
   与许多自动化工具不同，Windows MCP 不依赖任何传统的计算机视觉技术或特定的微调模型；它可以与任何 LLM 配合使用，降低了复杂性和设置时间。

- **丰富的 UI 自动化工具集**
  包含基本键盘、鼠标操作和捕获窗口/UI 状态的工具。

- **轻量级且开源**
  最小依赖和简单设置，完整源代码在 MIT 许可证下提供。

- **可定制和可扩展**
  轻松调整或扩展工具以满足您独特的自动化或 AI 集成需求。

- **实时交互**
  操作之间的典型延迟（例如，从一次鼠标点击到下一次）范围为 **1.5 到 2.3 秒**，可能会根据活动应用程序的数量和系统负载以及 LLM 的推理速度而略有不同。

### 先决条件

- Python 3.13+
- Anthropic Claude Desktop 应用程序或其他 MCP 客户端
- UV（包管理器）来自 Astra，使用 `pip install uv` 安装
- DXT（桌面扩展）来自 Anthropic，使用 `npm install -g @anthropic-ai/dxt` 安装
- 在 Windows 中设置 `English` 为默认语言

## 🏁 快速开始

### Gemini CLI

1. 在文件资源管理器中导航到 `%USERPROFILE%/.gemini` 并打开 `settings.json`。

2. 在 `settings.json` 中添加 `windows-mcp` 配置并保存。

```json
{
  "theme": "Default",
  ...
//MCP Server Config
  "mcpServers": {
    "windows-mcp": {
      "command": "uv",
      "args": [
        "--directory",
        "<windows-mcp 目录的路径>",
        "run",
        "main.py"
      ]
    }
  }
}
```

3. 在终端中重新运行 Gemini CLI。享受吧 🥳

### Claude Desktop

1. 克隆仓库。

```shell
git clone https://github.com/CursorTouch/Windows-MCP.git
cd Windows-MCP
```

2. 构建桌面扩展 `DXT`：

```shell
npx @anthropic-ai/dxt pack
```

3. 打开 Claude Desktop：

转到 Claude Desktop：设置->扩展->安装扩展（找到 `.dxt` 文件）-> 安装

最后享受吧 🥳。

有关 Claude Desktop 集成故障排除的更多信息，请参阅 [MCP 文档](https://modelcontextprotocol.io/quickstart/server#claude-for-desktop-integration-issues)。该文档包含检查日志和解决常见问题的有用提示。

---

## 🛠️MCP 工具

Claude 可以访问以下工具来与 Windows 交互：

- `Click-Tool`：在给定坐标处点击屏幕。
- `Type-Tool`：在元素上输入文本（可选择清除现有文本）。
- `Clipboard-Tool`：使用系统剪贴板复制或粘贴。
- `Scroll-Tool`：在窗口或特定区域垂直或水平滚动。
- `Drag-Tool`：从一个点拖拽到另一个点。
- `Move-Tool`：移动鼠标指针。
- `Shortcut-Tool`：按下键盘快捷键（`Ctrl+c`、`Alt+Tab` 等）。
- `Key-Tool`：按下单个按键。
- `Wait-Tool`：暂停指定的持续时间。
- `State-Tool`：默认语言、浏览器、活动应用程序和交互式、文本和可滚动元素的组合快照，以及桌面截图。
- `Resize-Tool`：用于更改应用程序的窗口大小或位置。
- `Launch-Tool`：从开始菜单启动应用程序。
- `Shell-Tool`：执行 PowerShell 命令。
- `Scrape-Tool`：抓取整个网页的信息。

## Star 历史

[![Star History Chart](https://api.star-history.com/svg?repos=CursorTouch/Windows-MCP&type=Date)](https://www.star-history.com/#CursorTouch/Windows-MCP&Date)

## ⚠️注意事项

此 MCP 直接与您的 Windows 操作系统交互以执行操作。请谨慎使用，避免在无法承受此类风险的环境中部署。

## 📝 限制

- 选择段落中文本的特定部分，因为 MCP 依赖于 a11y 树。（⌛ 正在开发中。）
- `Type-Tool` 用于输入文本，不适用于在 IDE 中编程，因为它会将程序作为整体输入到文件中。（⌛ 正在开发中。）

## 🪪许可证

本项目在 MIT 许可证下授权 - 详情请参阅 [LICENSE](LICENSE) 文件。

## 🤝贡献

欢迎贡献！请参阅 [CONTRIBUTING](CONTRIBUTING) 了解设置说明和开发指南。

由 [Jeomon George](https://github.com/Jeomon) 用 ❤️ 制作

## 引用

```bibtex
@software{
  author       = {George, Jeomon},
  title        = {Windows-MCP: Lightweight open-source project for integrating LLM agents with Windows},
  year         = {2024},
  publisher    = {GitHub},
  url={https://github.com/CursorTouch/Windows-MCP}
}
```
